<template>
  <q-page class="q-pa-md">
    <div class="test-grid-container">
      <div
        v-for="group in data.groups"
        :key="group.title"
        class="group-subgrid"
      >
        <!-- Header row with avatar and title -->
        <div class="avatar-cell">
          <q-avatar size="40px">
            <img src="/img/default_event.webp" alt="Event Avatar" />
          </q-avatar>
        </div>
        <div class="title-cell">
          {{ group.title }}
        </div>

        <!-- Data rows -->
        <template v-for="(item, index) in group.subItems" :key="index">
          <div class="data-cell data-cell-a">{{ item.cellA }}</div>
          <div class="data-cell data-cell-b">{{ item.cellB }}</div>
          <div class="data-cell data-cell-c">{{ item.cellC }}</div>
        </template>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from "vue";

interface SubItem {
  cellA: string;
  cellB: string;
  cellC: string;
}

interface Group {
  title: string;
  subItems: SubItem[];
}

interface TestData {
  groups: Group[];
}

//Generate random string helper
const generateRandomString = (min: number, max: number): string => {
  const length = Math.floor(Math.random() * (max - min + 1)) + min;
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

//Generate fake test data
const data = ref<TestData>({
  groups: [
    {
      title: "Presidential Election 2024 Markets",
      subItems: [
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        }
      ]
    },
    {
      title: "Cryptocurrency Price Predictions",
      subItems: [
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        }
      ]
    },
    {
      title: "Sports Betting Markets",
      subItems: [
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        },
        {
          cellA: generateRandomString(5, 15),
          cellB: generateRandomString(2, 8),
          cellC: generateRandomString(6, 10)
        }
      ]
    }
  ]
});
</script>

<style scoped>
.test-grid-container {
  display: grid;
  grid-template-columns: 60px max-content max-content max-content;
  gap: 16px 8px;
  max-width: 800px;
  margin: 0 auto;
}

.group-subgrid {
  display: grid;
  grid-template-columns: subgrid;
  grid-column: 1 / -1;
  gap: 8px;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
}

.avatar-cell {
  grid-row: 1 / -1;
  grid-column: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 4px;
  border: 2px solid red;
}

.title-cell {
  grid-column: 2 / -1;
  grid-row: 1;
  font-weight: bold;
  font-size: 16px;
  padding: 2px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  border: 2px solid blue;
  height: fit-content;
}

.data-cell {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  min-height: 32px;
  display: flex;
  align-items: center;
  border: 2px solid green;
}

.data-cell-a {
  grid-column: 2;
}

.data-cell-b {
  grid-column: 3;
}

.data-cell-c {
  grid-column: 4;
}
</style>
